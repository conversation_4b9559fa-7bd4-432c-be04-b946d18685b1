# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class ADXSwingBreakoutStrategy(IStrategy):

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '30m'
    can_short = False

    # ROI表 - 使用250 epochs最佳优化结果
    minimal_roi = {
        "0": 0.091,     # 9.1%止盈
        "174": 0.07,    # 174分钟后7.0%止盈
        "370": 0.041,   # 370分钟后4.1%止盈
        "502": 0        # 502分钟后0%止盈
    }

    # 止损设置 - 使用250 epochs最佳优化结果
    stoploss = -0.084  # 8.4%止损

    # 追踪止损 - 使用250 epochs最佳优化结果
    trailing_stop = True
    trailing_stop_positive = 0.011   # 1.1%正向追踪
    trailing_stop_positive_offset = 0.026  # 2.6%偏移
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量 - 降低以适应实时交易
    startup_candle_count: int = 20

    # 策略参数 - 使用250 epochs最佳优化结果 (5,511.63%收益率)
    adx_length = IntParameter(10, 20, default=15, space="buy", optimize=True)  # 最佳值: 15
    adx_base_level = IntParameter(10, 20, default=11, space="buy", optimize=True)  # 最佳值: 11
    adx_increment = IntParameter(5, 15, default=9, space="buy", optimize=True)  # 最佳值: 9
    swing_length_max = IntParameter(2, 10, default=7, space="buy", optimize=True)  # 最佳值: 7
    swing_length_min = IntParameter(1, 5, default=1, space="buy", optimize=True)  # 最佳值: 1

    # 杠杆参数 - 使用最佳优化结果
    leverage_optimize = IntParameter(4, 38, default=7, space="buy", optimize=True)  # 最佳值: 7

    # 出场参数 - 使用最佳优化结果
    exit_adx_threshold = IntParameter(12, 50, default=18, space="sell", optimize=True)  # 最佳值: 18
    
    # 订单类型
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    @property
    def protections(self):
        """
        保护机制配置
        """
        return [
            {
                # 止损保护：如果在过去24根蜡烛中有4次或更多止损，暂停交易2根蜡烛
                "method": "StoplossGuard",
                "lookback_period_candles": 24,
                "trade_limit": 4,
                "stop_duration_candles": 2,
                "only_per_pair": False
            },
            {
                # 最大回撤保护：如果过去48根蜡烛中有15笔交易导致超过15%回撤，暂停4根蜡烛
                "method": "MaxDrawdown",
                "lookback_period_candles": 48,
                "trade_limit": 15,
                "stop_duration_candles": 4,
                "max_allowed_drawdown": 0.15
            },
            {
                # 低盈利交易对保护：如果过去6根蜡烛中有2笔交易盈利低于1%，锁定该交易对30分钟
                "method": "LowProfitPairs",
                "lookback_period_candles": 6,
                "trade_limit": 2,
                "stop_duration_candles": 30,
                "required_profit": 0.01
            },
            {
                # 冷却期保护：卖出后冷却1根蜡烛再允许重新进入
                "method": "CooldownPeriod",
                "stop_duration_candles": 1
            }
        ]

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算ADX
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_length.value)
        
        # 根据ADX动态调整摆动周期
        dataframe['swing_bars'] = self.swing_length_max.value  # 默认值
        
        # ADX过滤逻辑
        adx_level1 = self.adx_base_level.value + self.adx_increment.value
        adx_level2 = self.adx_base_level.value + 2 * self.adx_increment.value
        adx_level3 = self.adx_base_level.value + 3 * self.adx_increment.value
        
        dataframe.loc[dataframe['adx'] < adx_level1, 'swing_bars'] = self.swing_length_max.value
        dataframe.loc[
            (dataframe['adx'] >= adx_level1) & (dataframe['adx'] < adx_level2), 
            'swing_bars'
        ] = max(3, self.swing_length_min.value + 2)
        dataframe.loc[
            (dataframe['adx'] >= adx_level2) & (dataframe['adx'] < adx_level3), 
            'swing_bars'
        ] = max(2, self.swing_length_min.value + 1)
        dataframe.loc[dataframe['adx'] >= adx_level3, 'swing_bars'] = self.swing_length_min.value
        
        # 识别摆动高低点
        dataframe['swing_high'] = False
        dataframe['swing_low'] = False
        dataframe['swing_high_price'] = np.nan
        dataframe['swing_low_price'] = np.nan
        
        # 简化的摆动点识别逻辑
        for i in range(2, len(dataframe) - 2):
            # 摆动高点：当前高点高于前后N根K线的高点
            swing_period = int(dataframe.iloc[i]['swing_bars'])
            
            # 检查摆动高点
            if i >= swing_period and i < len(dataframe) - swing_period:
                is_swing_high = True
                current_high = dataframe.iloc[i]['high']
                
                # 检查前后swing_period根K线
                for j in range(max(0, i - swing_period), min(len(dataframe), i + swing_period + 1)):
                    if j != i and dataframe.iloc[j]['high'] >= current_high:
                        is_swing_high = False
                        break
                
                if is_swing_high:
                    dataframe.iloc[i, dataframe.columns.get_loc('swing_high')] = True
                    dataframe.iloc[i, dataframe.columns.get_loc('swing_high_price')] = current_high
            
            # 检查摆动低点
            if i >= swing_period and i < len(dataframe) - swing_period:
                is_swing_low = True
                current_low = dataframe.iloc[i]['low']
                
                # 检查前后swing_period根K线
                for j in range(max(0, i - swing_period), min(len(dataframe), i + swing_period + 1)):
                    if j != i and dataframe.iloc[j]['low'] <= current_low:
                        is_swing_low = False
                        break
                
                if is_swing_low:
                    dataframe.iloc[i, dataframe.columns.get_loc('swing_low')] = True
                    dataframe.iloc[i, dataframe.columns.get_loc('swing_low_price')] = current_low
        
        # 向前填充最近的摆动高低点
        dataframe['last_swing_high'] = dataframe['swing_high_price'].ffill()
        dataframe['last_swing_low'] = dataframe['swing_low_price'].ffill()
        
        # 计算入场价格：摆动高点 + 1个最小变动单位
        # 这里简化为摆动高点 + 0.1%
        dataframe['long_entry_price'] = dataframe['last_swing_high'] * 1.001
        
        # 突破条件
        dataframe['breakout_condition'] = (
            (dataframe['high'] >= dataframe['long_entry_price']) &
            (dataframe['last_swing_high'].notna()) &
            (dataframe['adx'] > 20)  # ADX过滤，确保有趋势
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：突破摆动高点
        dataframe.loc[
            (
                (dataframe['breakout_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：ADX下降或出现新的摆动低点
        dataframe.loc[
            (
                (dataframe['adx'] < self.exit_adx_threshold.value) |  # ADX过低，趋势减弱
                (dataframe['swing_low'])   # 出现新的摆动低点
            ),
            'exit_long'] = 1

        return dataframe

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None,
                 side: str, **kwargs) -> float:
        """
        自定义杠杆设置
        """
        return self.leverage_optimize.value
