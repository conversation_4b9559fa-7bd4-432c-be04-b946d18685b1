# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file

# --- Do not remove these imports ---
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from pandas import DataFrame
from typing import Optional, Union

from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,  # @informative decorator
    # Hyperopt Parameters
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    # timeframe helpers
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    # Strategy helper functions
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
from technical import qtpylib


# This class is a sample. Feel free to customize it.
class ADXSwingBreakoutStrategy(IStrategy):
    """
    ADX Swing Breakout Strategy - Advanced Trend Following Strategy

    This strategy combines ADX trend strength analysis with swing point breakout detection
    to identify high-probability trend continuation opportunities.

    Key Features:
    - Dynamic ADX-based swing period adjustment
    - Swing high/low breakout detection
    - Comprehensive protection mechanisms
    - Optimized for futures trading with leverage

    More information in https://www.freqtrade.io/en/latest/strategy-customization/

    You can:
        :return: a Dataframe with all mandatory indicators for the strategies
    - Rename the class name (Do not forget to update class_name)
    - Add any methods you want to build your strategy
    - Add any lib you need to build your strategy

    You must keep:
    - the lib in the section "Do not remove these libs"
    - the methods: populate_indicators, populate_entry_trend, populate_exit_trend
    You should keep:
    - timeframe, minimal_roi, stoploss, trailing_*
    """

    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.
    INTERFACE_VERSION = 3

    # Can this strategy go short?
    can_short: bool = False

    # Static ROI and stoploss values (used as defaults, overridden by dynamic properties)
    # These will be replaced by the dynamic @property methods below for optimization

    # Trailing stoploss configuration (static settings)
    trailing_stop = True
    trailing_only_offset_is_reached = True

    # Optimal timeframe for the strategy.
    timeframe = "30m"

    # Run "populate_indicators()" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 20

    # Hyperoptable parameters - Strategy Logic Parameters
    adx_length = IntParameter(
        low=10, high=20, default=15, space="buy", optimize=True, load=True
    )  # ADX calculation period - Optimal: 15

    adx_base_level = IntParameter(
        low=10, high=20, default=12, space="buy", optimize=True, load=True
    )  # ADX base threshold level - Optimal: 11

    adx_increment = IntParameter(
        low=5, high=15, default=9, space="buy", optimize=True, load=True
    )  # ADX increment step for dynamic adjustment - Optimal: 9

    swing_length_max = IntParameter(
        low=2, high=10, default=7, space="buy", optimize=True, load=True
    )  # Maximum swing detection period - Optimal: 7

    swing_length_min = IntParameter(
        low=1, high=5, default=1, space="buy", optimize=True, load=True
    )  # Minimum swing detection period - Optimal: 1

    # Leverage optimization parameter
    leverage_optimize = IntParameter(
        low=2, high=18, default=7, space="buy", optimize=True, load=True
    )  # Leverage multiplier - Optimal: 7

    # Exit parameters
    exit_adx_threshold = IntParameter(
        low=12, high=50, default=18, space="sell", optimize=True, load=True
    )  # ADX threshold for exit signal - Optimal: 18

    # ROI Optimization Parameters
    roi_time_1 = IntParameter(
        low=50, high=300, default=174, space="roi", optimize=True, load=True
    )  # First ROI time threshold (minutes)

    roi_time_2 = IntParameter(
        low=200, high=600, default=370, space="roi", optimize=True, load=True
    )  # Second ROI time threshold (minutes)

    roi_time_3 = IntParameter(
        low=400, high=1000, default=502, space="roi", optimize=True, load=True
    )  # Third ROI time threshold (minutes)

    roi_profit_1 = DecimalParameter(
        low=0.05, high=0.15, default=0.091, decimals=3, space="roi", optimize=True, load=True
    )  # Initial ROI profit target

    roi_profit_2 = DecimalParameter(
        low=0.03, high=0.10, default=0.07, decimals=3, space="roi", optimize=True, load=True
    )  # Second ROI profit target

    roi_profit_3 = DecimalParameter(
        low=0.01, high=0.08, default=0.041, decimals=3, space="roi", optimize=True, load=True
    )  # Third ROI profit target

    # Stoploss Optimization Parameters
    stoploss_optimize = DecimalParameter(
        low=-0.15, high=-0.05, default=-0.084, decimals=3, space="stoploss", optimize=True, load=True
    )  # Dynamic stoploss optimization

    # Trailing Stop Optimization Parameters
    trailing_stop_positive_optimize = DecimalParameter(
        low=0.005, high=0.03, default=0.011, decimals=3, space="trailing", optimize=True, load=True
    )  # Trailing stop positive threshold

    trailing_stop_positive_offset_optimize = DecimalParameter(
        low=0.01, high=0.05, default=0.026, decimals=3, space="trailing", optimize=True, load=True
    )  # Trailing stop positive offset

    # Protection Parameters (optimization enabled)
    protection_enabled = BooleanParameter(
        default=True, space="protection", optimize=True, load=True
    )  # Enable/disable protection mechanisms

    stoploss_guard_lookback = IntParameter(
        low=12, high=48, default=24, space="protection", optimize=True, load=True
    )  # Stoploss guard lookback period

    stoploss_guard_trade_limit = IntParameter(
        low=2, high=8, default=4, space="protection", optimize=True, load=True
    )  # Stoploss guard trade limit

    max_drawdown_lookback = IntParameter(
        low=24, high=96, default=48, space="protection", optimize=True, load=True
    )  # Max drawdown lookback period

    max_drawdown_trade_limit = IntParameter(
        low=8, high=25, default=15, space="protection", optimize=True, load=True
    )  # Max drawdown trade limit

    # Optional order type mapping.
    order_types = {
        "entry": "limit",
        "exit": "limit",
        "stoploss": "market",
        "stoploss_on_exchange": False,
    }

    # Optional order time in force.
    order_time_in_force = {"entry": "GTC", "exit": "GTC"}

    # Dynamic ROI and stoploss will be handled in bot_start() method
    # These are default values that will be overridden
    minimal_roi = {
        0: 0.091,
        174: 0.07,
        370: 0.041,
        502: 0
    }

    stoploss = -0.084
    trailing_stop_positive = 0.011
    trailing_stop_positive_offset = 0.026

    def bot_start(self, **kwargs) -> None:
        """
        Called only once after bot instantiation.
        This method allows dynamic parameter assignment based on hyperopt values.
        """
        # Update ROI table with optimized values (keys must be integers)
        self.minimal_roi = {
            0: self.roi_profit_1.value,
            self.roi_time_1.value: self.roi_profit_2.value,
            self.roi_time_2.value: self.roi_profit_3.value,
            self.roi_time_3.value: 0
        }

        # Update stoploss with optimized value
        self.stoploss = self.stoploss_optimize.value

        # Update trailing stop parameters with optimized values
        self.trailing_stop_positive = self.trailing_stop_positive_optimize.value
        self.trailing_stop_positive_offset = self.trailing_stop_positive_offset_optimize.value

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        These pair/interval combinations are non-tradeable, unless they are part
        of the whitelist as well.
        For more information, please consult the documentation
        :return: List of tuples in the format (pair, interval)
            Sample: return [("ETH/USDT", "5m"),
                            ("BTC/USDT", "15m"),
                            ]
        """
        return []

    @property
    def protections(self):
        """
        Define protection mechanisms for the strategy.

        This strategy implements multiple protection layers with optimizable parameters:
        - StoplossGuard: Prevents excessive losses from stop-loss triggers
        - MaxDrawdown: Protects against large portfolio drawdowns
        - LowProfitPairs: Filters out underperforming pairs
        - CooldownPeriod: Prevents immediate re-entry after exit

        :return: List of protection configurations
        """
        prot = []

        if self.protection_enabled.value:
            prot.extend([
                {
                    # Stop-loss protection: Pause trading if multiple stop-losses occur
                    "method": "StoplossGuard",
                    "lookback_period_candles": self.stoploss_guard_lookback.value,
                    "trade_limit": self.stoploss_guard_trade_limit.value,
                    "stop_duration_candles": 2,
                    "only_per_pair": False
                },
                {
                    # Max drawdown protection: Pause if trades cause significant drawdown
                    "method": "MaxDrawdown",
                    "lookback_period_candles": self.max_drawdown_lookback.value,
                    "trade_limit": self.max_drawdown_trade_limit.value,
                    "stop_duration_candles": 4,
                    "max_allowed_drawdown": 0.15
                },
                {
                    # Low profit pairs protection: Lock pairs with <1% profit in 6 candles
                    "method": "LowProfitPairs",
                    "lookback_period_candles": 6,
                    "trade_limit": 2,
                    "stop_duration_candles": 30,
                    "required_profit": 0.01
                },
                {
                    # Cooldown period: Wait 1 candle after exit before re-entry
                    "method": "CooldownPeriod",
                    "stop_duration_candles": 1
                }
            ])

        return prot

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame

        Performance Note: For the best performance be frugal on the number of indicators
        you are using. Let uncomment only the indicator you are using in your strategies
        or your hyperopt configuration, otherwise you will waste your memory and CPU usage.

        :param dataframe: Dataframe with data from the exchange
        :param metadata: Additional information, like the currently traded pair
        :return: a Dataframe with all mandatory indicators for the strategies
        """

        # Momentum Indicators
        # ------------------------------------

        # ADX - Average Directional Index (Trend Strength)
        dataframe["adx"] = ta.ADX(dataframe, timeperiod=self.adx_length.value)

        # Dynamic swing period calculation based on ADX strength
        # Higher ADX = stronger trend = shorter swing periods for faster signals
        dataframe["swing_bars"] = self.swing_length_max.value  # Default value

        # Calculate ADX threshold levels for dynamic adjustment
        adx_level1 = self.adx_base_level.value + self.adx_increment.value
        adx_level2 = self.adx_base_level.value + 2 * self.adx_increment.value
        adx_level3 = self.adx_base_level.value + 3 * self.adx_increment.value

        # Apply dynamic swing period based on ADX strength
        # Weak trend (low ADX): Use maximum swing period for stability
        dataframe.loc[dataframe["adx"] < adx_level1, "swing_bars"] = self.swing_length_max.value

        # Moderate trend: Use medium swing period
        dataframe.loc[
            (dataframe["adx"] >= adx_level1) & (dataframe["adx"] < adx_level2),
            "swing_bars"
        ] = max(3, self.swing_length_min.value + 2)

        # Strong trend: Use shorter swing period
        dataframe.loc[
            (dataframe["adx"] >= adx_level2) & (dataframe["adx"] < adx_level3),
            "swing_bars"
        ] = max(2, self.swing_length_min.value + 1)

        # Very strong trend: Use minimum swing period for quick signals
        dataframe.loc[dataframe["adx"] >= adx_level3, "swing_bars"] = self.swing_length_min.value

        # Swing Point Detection
        # ------------------------------------

        # Initialize swing point columns
        dataframe["swing_high"] = False
        dataframe["swing_low"] = False
        dataframe["swing_high_price"] = np.nan
        dataframe["swing_low_price"] = np.nan

        # Swing point identification algorithm
        # This identifies local highs and lows based on dynamic swing periods
        for i in range(2, len(dataframe) - 2):
            swing_period = int(dataframe.iloc[i]["swing_bars"])

            # Swing High Detection
            # A swing high is a peak that is higher than surrounding candles
            if i >= swing_period and i < len(dataframe) - swing_period:
                is_swing_high = True
                current_high = dataframe.iloc[i]["high"]

                # Check if current high is higher than all surrounding candles
                for j in range(max(0, i - swing_period), min(len(dataframe), i + swing_period + 1)):
                    if j != i and dataframe.iloc[j]["high"] >= current_high:
                        is_swing_high = False
                        break

                if is_swing_high:
                    dataframe.iloc[i, dataframe.columns.get_loc("swing_high")] = True
                    dataframe.iloc[i, dataframe.columns.get_loc("swing_high_price")] = current_high

            # Swing Low Detection
            # A swing low is a trough that is lower than surrounding candles
            if i >= swing_period and i < len(dataframe) - swing_period:
                is_swing_low = True
                current_low = dataframe.iloc[i]["low"]

                # Check if current low is lower than all surrounding candles
                for j in range(max(0, i - swing_period), min(len(dataframe), i + swing_period + 1)):
                    if j != i and dataframe.iloc[j]["low"] <= current_low:
                        is_swing_low = False
                        break

                if is_swing_low:
                    dataframe.iloc[i, dataframe.columns.get_loc("swing_low")] = True
                    dataframe.iloc[i, dataframe.columns.get_loc("swing_low_price")] = current_low

        # Forward fill the most recent swing points for breakout detection
        dataframe["last_swing_high"] = dataframe["swing_high_price"].ffill()
        dataframe["last_swing_low"] = dataframe["swing_low_price"].ffill()

        # Entry Price Calculation
        # ------------------------------------

        # Calculate long entry price: swing high + small buffer (0.1%)
        # This ensures we enter on actual breakout, not just touching the level
        dataframe["long_entry_price"] = dataframe["last_swing_high"] * 1.001

        # Breakout Condition Logic
        # ------------------------------------

        # Define breakout conditions for entry signals
        dataframe["breakout_condition"] = (
            (dataframe["high"] >= dataframe["long_entry_price"]) &  # Price breaks above swing high
            (dataframe["last_swing_high"].notna()) &  # Valid swing high exists
            (dataframe["adx"] > 20)  # ADX filter: ensure sufficient trend strength
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe

        Entry Logic:
        - Long Entry: Breakout above swing high with sufficient volume and ADX confirmation

        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with entry columns populated
        """

        # Long Entry Signal: Swing High Breakout
        dataframe.loc[
            (
                # Primary condition: Breakout above swing high
                (dataframe["breakout_condition"]) &
                # Volume filter: Ensure sufficient trading activity
                (dataframe["volume"] > 0)
            ),
            "enter_long",
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe

        Exit Logic:
        - Long Exit: ADX weakening below threshold OR new swing low formation

        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with exit columns populated
        """

        # Long Exit Signal: Trend Weakness or Swing Low
        dataframe.loc[
            (
                # Exit condition 1: ADX below threshold (trend weakening)
                (dataframe["adx"] < self.exit_adx_threshold.value) |
                # Exit condition 2: New swing low formed (potential reversal)
                (dataframe["swing_low"])
            ),
            "exit_long",
        ] = 1

        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str | None,
        side: str,
        **kwargs,
    ) -> float:
        """
        Customize leverage for each trade.

        This method allows dynamic leverage adjustment based on market conditions,
        pair characteristics, or other factors.

        :param pair: Trading pair
        :param current_time: Current timestamp
        :param current_rate: Current price
        :param proposed_leverage: Leverage proposed by the bot
        :param max_leverage: Maximum allowed leverage
        :param entry_tag: Entry tag if provided
        :param side: Trade side (long/short)
        :return: Leverage to use for this trade
        """
        # Return optimized leverage value
        # Bot logic will ensure it's within allowed limits and adjust if necessary
        return self.leverage_optimize.value
