{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 3, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "timeframe": "30m", "dry_run": true, "cancel_open_orders_on_exit": false, "strategy": "ADXSwingBreakoutStrategy", "trading_mode": "futures", "margin_mode": "isolated", "minimal_roi": {"0": 0.389, "100": 0.152, "337": 0.046, "949": 0}, "stoploss": -0.23, "trailing_stop": true, "trailing_stop_positive": 0.019, "trailing_stop_positive_offset": 0.061, "trailing_only_offset_is_reached": true, "use_exit_signal": true, "exit_profit_only": false, "ignore_roi_if_entry_signal": false, "position_adjustment_enable": false, "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "order_types": {"entry": "market", "exit": "market", "stoploss": "market", "stoploss_on_exchange": true, "stoploss_price_type": "last"}, "order_time_in_force": {"entry": "GTC", "exit": "GTC"}, "exchange": {"name": "binance", "key": "4WAy2gwqtbjDyoP716wTLKGLPgSL9EwAQtULH4Ab7RKKJenToYTYaj5KxAJgmJpP", "secret": "5wlN34C2EIKQ6bnyE7qNp8aW2NeIOuCYjH8hxY8wwaTnnwR7G4q5qnmTtD1Gl2U5", "ccxt_config": {"httpsProxy": "http://*************:12334", "wsProxy": "http://*************:12334"}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "SUI/USDT:USDT", "DOGE/USDT:USDT", "XRP/USDT:USDT"], "pair_blacklist": [".*_.*"]}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": true, "token": "**********************************************", "chat_id": "8092782083"}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8083, "verbosity": "error", "jwt_secret_key": "holy_grail_v1_secret_key", "CORS_origins": [], "username": "bot", "password": "admin"}, "bot_name": "ADXSwingBreakoutStrategy", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 2}}