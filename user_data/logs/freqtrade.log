2025-08-08 01:44:47,793 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-08 01:44:47,794 - root - INFO - Logfile configured
2025-08-08 01:44:47,794 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-08 01:44:47,795 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-08 01:44:47,796 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-08 01:44:47,796 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-08 01:44:47,797 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-08 01:44:47,797 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-08 01:44:47,814 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-08 01:44:47,815 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-08 01:44:47,816 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-08 01:44:47,828 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-08 01:44:47,829 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-08 01:44:47,903 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-08 01:44:47,904 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-08-08 01:44:47,905 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-08 01:44:47,906 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-08 01:44:47,907 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-08 01:44:47,907 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-08 01:44:47,908 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-08 01:44:47,909 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-08 01:44:47,909 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-08 01:44:47,910 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-08 01:44:47,910 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-08 01:44:47,911 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-08 01:44:47,912 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-08 01:44:47,912 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-08 01:44:47,913 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-08 01:44:47,913 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-08 01:44:47,914 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-08 01:44:47,914 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-08 01:44:47,915 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-08 01:44:47,915 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-08 01:44:47,916 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-08 01:44:47,917 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-08 01:44:47,917 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-08 01:44:47,918 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-08 01:44:47,918 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-08 01:44:47,919 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-08 01:44:47,919 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-08 01:44:47,920 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-08 01:44:47,921 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-08 01:44:47,921 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-08 01:44:47,922 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-08 01:44:47,922 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-08 01:44:47,923 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-08 01:44:47,923 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-08 01:44:47,924 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-08 01:44:47,925 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-08 01:44:47,925 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-08 01:44:47,926 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-08 01:44:47,926 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-08 01:44:47,927 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-08 01:44:47,927 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-08 01:44:47,928 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-08 01:44:47,928 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-08 01:44:47,929 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-08 01:44:47,932 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-08 01:44:47,932 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-08 01:44:47,933 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://192.168.127.1:12334', 'wsProxy': 'http://192.168.127.1:12334'}
2025-08-08 01:44:47,943 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://192.168.127.1:12334', 'wsProxy': 'http://192.168.127.1:12334'}
2025-08-08 01:44:47,955 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-08 01:44:50,353 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-08-08 01:44:50,403 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 01:44:51,251 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-08-08 01:44:51,416 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-08-08 01:44:51,655 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8083
2025-08-08 01:44:51,656 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-08-08 01:44:51,719 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-08-08 01:44:51,720 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-08-08 01:44:51,736 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 6 pairs: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'SOL/USDT:USDT', 'SUI/USDT:USDT', 'DOGE/USDT:USDT', 'XRP/USDT:USDT']
2025-08-08 01:44:51,737 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.02s
2025-08-08 01:44:51,738 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-08-08 01:44:51,739 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): adx_base_level = 18
2025-08-08 01:44:51,740 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): adx_increment = 9
2025-08-08 01:44:51,741 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): adx_length = 12
2025-08-08 01:44:51,741 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): leverage_optimize = 8
2025-08-08 01:44:51,742 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): swing_length_max = 8
2025-08-08 01:44:51,742 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): swing_length_min = 1
2025-08-08 01:44:51,743 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-08-08 01:44:51,744 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): exit_adx_threshold = 16
2025-08-08 01:44:51,744 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-08-08 01:44:51,750 - freqtrade.resolvers.iresolver - INFO - Using resolved protection StoplossGuard from '/freqtrade/freqtrade/plugins/protections/stoploss_guard.py'...
2025-08-08 01:44:51,754 - freqtrade.resolvers.iresolver - INFO - Using resolved protection MaxDrawdown from '/freqtrade/freqtrade/plugins/protections/max_drawdown_protection.py'...
2025-08-08 01:44:51,758 - freqtrade.resolvers.iresolver - INFO - Using resolved protection LowProfitPairs from '/freqtrade/freqtrade/plugins/protections/low_profit_pairs.py'...
2025-08-08 01:44:51,764 - freqtrade.resolvers.iresolver - INFO - Using resolved protection CooldownPeriod from '/freqtrade/freqtrade/plugins/protections/cooldown_period.py'...
2025-08-08 01:44:51,765 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-08-08 01:44:51,766 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-08-08 01:44:51,773 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-08-08 01:44:51,774 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `binance`\n*Stake per trade:* `unlimited USDT`\n*Minimum ROI:* `{'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}`\n*Trailing Stoploss:* `-0.23`\n*Position adjustment:* `Off`\n*Timeframe:* `30m`\n*Strategy:* `ADXSwingBreakoutStrategy`"}
2025-08-08 01:44:51,775 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-08-08 01:44:51,776 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': 'Using Protections: \nStoplossGuard - Frequent Stoploss Guard, 4 stoplosses with profit < 0.00% within 24 candles.\nMaxDrawdown - Max drawdown protection, stop trading if drawdown is > 0.15 within 48 candles.\nLowProfitPairs - Low Profit Protection, locks pairs with profit < 0.01 within 6 candles.\nCooldownPeriod - Cooldown period for 1 candle.'}
2025-08-08 01:44:51,781 - freqtrade.persistence.trade_model - INFO - Found open trade: Trade(id=5, pair=BTC/USDT:USDT, amount=0.02000000, is_short=False, leverage=7.0, open_rate=116345.60000000, open_since=2025-08-07 11:00:24)
2025-08-08 01:44:51,782 - freqtrade.persistence.trade_model - INFO - Found open trade: Trade(id=7, pair=SOL/USDT:USDT, amount=14.01000000, is_short=False, leverage=7.0, open_rate=173.14000000, open_since=2025-08-07 11:00:28)
2025-08-08 01:44:51,782 - freqtrade.persistence.trade_model - INFO - Found open trade: Trade(id=10, pair=SUI/USDT:USDT, amount=810.50000000, is_short=False, leverage=8.0, open_rate=3.74730000, open_since=2025-08-08 01:34:46)
2025-08-08 01:44:51,793 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-08-08 01:44:52,863 - telegram.ext.Application - INFO - Application started
2025-08-08 01:45:04,634 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:45:13,342 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(6f1c8aa7, ('172.18.0.1', 34818))
2025-08-08 01:45:13,585 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(d65659d3, ('172.18.0.1', 34880))
2025-08-08 01:46:04,656 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:46:18,952 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(6f1c8aa7, ('172.18.0.1', 34818))
2025-08-08 01:46:18,954 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(d65659d3, ('172.18.0.1', 34880))
2025-08-08 01:47:04,676 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:48:04,697 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:49:04,718 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:50:04,741 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:51:04,762 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:51:29,079 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(a53ff68d, ('172.18.0.1', 34408))
2025-08-08 01:51:29,160 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(0ae4a113, ('172.18.0.1', 34418))
2025-08-08 01:51:29,313 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(d867f182, ('172.18.0.1', 34434))
2025-08-08 01:52:04,783 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:52:45,275 - freqtrade.freqtradebot - INFO - Exit for SOL/USDT:USDT detected. Reason: roi
2025-08-08 01:52:46,519 - freqtrade.freqtradebot - INFO - Cancelling stoploss on exchange for Trade(id=7, pair=SOL/USDT:USDT, amount=14.01000000, is_short=False, leverage=7.0, open_rate=173.14000000, open_since=2025-08-07 11:00:28) order: dry_run_sell_SOL/USDT:USDT_1754617504.059296
2025-08-08 01:52:46,527 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 01:52:46,679 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 7, 'exchange': 'Binance', 'pair': 'SOL/USDT:USDT', 'leverage': 7.0, 'direction': 'Long', 'gain': 'profit', 'limit': 174.49, 'order_rate': 174.49, 'order_type': 'market', 'amount': 14.01, 'open_rate': 173.14, 'close_rate': 174.49, 'current_rate': 174.49, 'profit_amount': 16.21488052, 'profit_ratio': 0.04676911, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'roi', 'open_date': datetime.datetime(2025, 8, 7, 11, 0, 28, 515045, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 8, 8, 1, 52, 46, 679597, tzinfo=datetime.timezone.utc), 'stake_amount': 346.52734285714286, 'stake_currency': 'USDT', 'base_currency': 'SOL', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-08-08 01:52:46,681 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=7, pair=SOL/USDT:USDT, amount=14.01000000, is_short=False, leverage=7.0, open_rate=173.14000000, open_since=2025-08-07 11:00:28)
2025-08-08 01:52:46,688 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=7, pair=SOL/USDT:USDT, amount=14.01000000, is_short=False, leverage=7.0, open_rate=173.14000000, open_since=2025-08-07 11:00:28) [sell]: 1.2223025 USDT - rate: 0.0005
2025-08-08 01:52:46,689 - freqtrade.persistence.trade_model - INFO - Updating trade (id=7) ...
2025-08-08 01:52:46,690 - freqtrade.persistence.trade_model - INFO - MARKET_SELL has been fulfilled for Trade(id=7, pair=SOL/USDT:USDT, amount=14.01000000, is_short=False, leverage=7.0, open_rate=173.14000000, open_since=2025-08-07 11:00:28).
2025-08-08 01:52:46,691 - freqtrade.persistence.trade_model - INFO - Marking Trade(id=7, pair=SOL/USDT:USDT, amount=14.01000000, is_short=False, leverage=7.0, open_rate=173.14000000, open_since=closed) as closed as the trade is fulfilled and found no open orders for it.
2025-08-08 01:52:46,697 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 01:52:46,699 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit_fill, 'trade_id': 7, 'exchange': 'Binance', 'pair': 'SOL/USDT:USDT', 'leverage': 7.0, 'direction': 'Long', 'gain': 'profit', 'limit': 174.49, 'order_rate': 174.49, 'order_type': 'market', 'amount': 14.01, 'open_rate': 173.14, 'close_rate': 174.49, 'current_rate': None, 'profit_amount': 16.21488052, 'profit_ratio': 0.04676911, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'roi', 'open_date': datetime.datetime(2025, 8, 7, 11, 0, 28, 515045, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 8, 8, 1, 52, 46, 679000, tzinfo=datetime.timezone.utc), 'stake_amount': 346.52734285714286, 'stake_currency': 'USDT', 'base_currency': 'SOL', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 16.21488052, 'final_profit_ratio': 0.046769114950088043, 'is_final_exit': True}
2025-08-08 01:52:46,707 - cooldown_period - INFO - Cooldown for SOL/USDT:USDT for 1 candle.
2025-08-08 01:52:46,711 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': protection_trigger, 'base_currency': 'SOL', 'id': 16, 'pair': 'SOL/USDT:USDT', 'lock_time': '2025-08-08 01:52:46', 'lock_timestamp': 1754617966703, 'lock_end_time': '2025-08-08 02:30:00', 'lock_end_timestamp': 1754620200000, 'reason': 'Cooldown period for for 1 candle.', 'side': '*', 'active': True}
2025-08-08 01:52:46,934 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 01:52:46,947 - freqtrade.freqtradebot - INFO - Pair SOL/USDT:USDT * is locked until 2025-08-08 02:30:00 due to Cooldown period for for 1 candle..
2025-08-08 01:52:46,952 - freqtrade.freqtradebot - INFO - Pair DOGE/USDT:USDT * is locked until 2025-08-08 02:30:00 due to Cooldown period for for 1 candle..
2025-08-08 01:53:04,962 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:54:04,982 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:55:05,005 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:56:05,024 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:57:05,055 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:58:05,076 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 01:59:05,097 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:00:05,855 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:01:06,023 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 02:01:06,026 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:02:06,046 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:03:06,065 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:04:06,085 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:05:06,105 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:06:06,124 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:07:06,143 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:07:30,226 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to RequestTimeout. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:07:35,246 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:07:40,263 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:07:42,911 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        pool_request.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpcore/_async/http_proxy.py", line 343, in handle_async_request
    return await self._connection.handle_async_request(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpcore/_async/http11.py", line 231, in _receive_event
    raise RemoteProtocolError(msg)
httpcore.RemoteProtocolError: Server disconnected without sending a response.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/usr/local/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.RemoteProtocolError: Server disconnected without sending a response.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/_bot.py", line 4610, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/request/_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.RemoteProtocolError: Server disconnected without sending a response.
2025-08-08 02:07:45,277 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:07:50,322 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Giving up.
2025-08-08 02:07:50,324 - freqtrade.freqtradebot - WARNING - Unable to exit trade BTC/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5
2025-08-08 02:07:55,339 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:08:00,359 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:08:05,369 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:08:10,380 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:08:15,397 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Giving up.
2025-08-08 02:08:15,398 - freqtrade.freqtradebot - WARNING - Unable to exit trade SUI/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5
2025-08-08 02:08:15,414 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:08:20,503 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:08:25,511 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:08:30,546 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:08:35,552 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:08:40,566 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Giving up.
2025-08-08 02:08:40,567 - freqtrade.freqtradebot - WARNING - Unable to exit trade BTC/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5
2025-08-08 02:08:45,584 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:08:50,592 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:08:55,640 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:09:00,652 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:09:05,672 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Giving up.
2025-08-08 02:09:05,673 - freqtrade.freqtradebot - WARNING - Unable to exit trade SUI/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5
2025-08-08 02:09:10,729 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:09:15,736 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:09:20,749 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:09:25,757 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:09:30,769 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Giving up.
2025-08-08 02:09:30,770 - freqtrade.freqtradebot - WARNING - Unable to exit trade BTC/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5
2025-08-08 02:09:35,787 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:09:40,798 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:09:45,813 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:09:50,823 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:09:55,832 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Giving up.
2025-08-08 02:09:55,833 - freqtrade.freqtradebot - WARNING - Unable to exit trade SUI/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5
2025-08-08 02:09:55,843 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:10:00,885 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:10:05,900 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:10:10,914 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:10:15,989 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:10:21,149 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Giving up.
2025-08-08 02:10:21,151 - freqtrade.freqtradebot - WARNING - Unable to exit trade BTC/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5
2025-08-08 02:10:26,171 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:10:31,180 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:10:36,189 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:10:41,195 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:10:46,235 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Giving up.
2025-08-08 02:10:46,237 - freqtrade.freqtradebot - WARNING - Unable to exit trade SUI/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5
2025-08-08 02:10:51,304 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:10:56,339 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:11:01,355 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:11:06,362 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:11:11,380 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Giving up.
2025-08-08 02:11:11,381 - freqtrade.freqtradebot - WARNING - Unable to exit trade BTC/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5
2025-08-08 02:11:16,430 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:11:21,449 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:11:26,464 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:11:31,481 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:11:36,501 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Giving up.
2025-08-08 02:11:36,502 - freqtrade.freqtradebot - WARNING - Unable to exit trade SUI/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5
2025-08-08 02:11:36,518 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:11:41,574 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:11:46,589 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:11:51,604 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:11:56,620 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:12:01,631 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Giving up.
2025-08-08 02:12:01,632 - freqtrade.freqtradebot - WARNING - Unable to exit trade BTC/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5
2025-08-08 02:12:06,653 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:12:11,659 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:12:16,679 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:12:21,719 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:12:26,733 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5". Giving up.
2025-08-08 02:12:26,734 - freqtrade.freqtradebot - WARNING - Unable to exit trade SUI/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=SUIUSDT&limit=5
2025-08-08 02:12:31,829 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:12:36,875 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 3 times.
2025-08-08 02:12:41,890 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 2 times.
2025-08-08 02:12:46,907 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 1 times.
2025-08-08 02:12:51,926 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Giving up.
2025-08-08 02:12:51,927 - freqtrade.freqtradebot - WARNING - Unable to exit trade BTC/USDT:USDT: Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5
2025-08-08 02:12:52,675 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:13:52,695 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:14:52,715 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:15:52,732 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:16:52,750 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:17:52,777 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:18:52,798 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:19:52,816 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:20:52,870 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:21:52,891 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:22:47,276 - freqtrade.freqtradebot - INFO - Pair SOL/USDT:USDT * is locked until 2025-08-08 02:30:00 due to Cooldown period for for 1 candle..
2025-08-08 02:22:52,908 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:23:34,624 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to NetworkError. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5". Retrying still for 4 times.
2025-08-08 02:23:53,589 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:24:53,609 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:25:53,629 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:26:53,648 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:27:53,669 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:28:53,688 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:29:53,706 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:30:53,742 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:31:05,676 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 02:31:55,692 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:32:55,710 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:33:53,246 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(0ae4a113, ('172.18.0.1', 34418))
2025-08-08 02:33:53,255 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(a53ff68d, ('172.18.0.1', 34408))
2025-08-08 02:33:53,258 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(d867f182, ('172.18.0.1', 34434))
2025-08-08 02:33:54,697 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(09200972, ('172.18.0.1', 53192))
2025-08-08 02:33:54,948 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(486206cf, ('172.18.0.1', 53200))
2025-08-08 02:33:57,101 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:34:57,122 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:35:57,141 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:36:57,158 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:37:57,177 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:38:57,193 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:39:57,210 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:40:57,227 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:41:57,246 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:42:57,276 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:43:57,326 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:44:57,956 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 6 pairs: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'SOL/USDT:USDT', 'SUI/USDT:USDT', 'DOGE/USDT:USDT', 'XRP/USDT:USDT']
2025-08-08 02:44:58,410 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:45:58,437 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:46:58,456 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:47:58,502 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:48:58,528 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:49:24,812 - freqtrade.freqtradebot - INFO - Exit for BTC/USDT:USDT detected. Reason: roi
2025-08-08 02:49:27,161 - freqtrade.freqtradebot - INFO - Cancelling stoploss on exchange for Trade(id=5, pair=BTC/USDT:USDT, amount=0.02000000, is_short=False, leverage=7.0, open_rate=116345.60000000, open_since=2025-08-07 11:00:24) order: dry_run_sell_BTC/USDT:USDT_1754617503.043135
2025-08-08 02:49:27,244 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 02:49:27,521 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 5, 'exchange': 'Binance', 'pair': 'BTC/USDT:USDT', 'leverage': 7.0, 'direction': 'Long', 'gain': 'profit', 'limit': 116818.3, 'order_rate': 116818.3, 'order_type': 'market', 'amount': 0.02, 'open_rate': 116345.6, 'close_rate': 116818.3, 'current_rate': 116818.3, 'profit_amount': 6.91955257, 'profit_ratio': 0.02080554, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'roi', 'open_date': datetime.datetime(2025, 8, 7, 11, 0, 24, 532747, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 8, 8, 2, 49, 27, 517781, tzinfo=datetime.timezone.utc), 'stake_amount': 332.416, 'stake_currency': 'USDT', 'base_currency': 'BTC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-08-08 02:49:27,586 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=5, pair=BTC/USDT:USDT, amount=0.02000000, is_short=False, leverage=7.0, open_rate=116345.60000000, open_since=2025-08-07 11:00:24)
2025-08-08 02:49:27,760 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=5, pair=BTC/USDT:USDT, amount=0.02000000, is_short=False, leverage=7.0, open_rate=116345.60000000, open_since=2025-08-07 11:00:24) [sell]: 1.168183 USDT - rate: 0.0005
2025-08-08 02:49:27,771 - freqtrade.persistence.trade_model - INFO - Updating trade (id=5) ...
2025-08-08 02:49:27,793 - freqtrade.persistence.trade_model - INFO - MARKET_SELL has been fulfilled for Trade(id=5, pair=BTC/USDT:USDT, amount=0.02000000, is_short=False, leverage=7.0, open_rate=116345.60000000, open_since=2025-08-07 11:00:24).
2025-08-08 02:49:27,800 - freqtrade.persistence.trade_model - INFO - Marking Trade(id=5, pair=BTC/USDT:USDT, amount=0.02000000, is_short=False, leverage=7.0, open_rate=116345.60000000, open_since=closed) as closed as the trade is fulfilled and found no open orders for it.
2025-08-08 02:49:27,901 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 02:49:27,915 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit_fill, 'trade_id': 5, 'exchange': 'Binance', 'pair': 'BTC/USDT:USDT', 'leverage': 7.0, 'direction': 'Long', 'gain': 'profit', 'limit': 116818.3, 'order_rate': 116818.3, 'order_type': 'market', 'amount': 0.02, 'open_rate': 116345.6, 'close_rate': 116818.3, 'current_rate': None, 'profit_amount': 6.91955257, 'profit_ratio': 0.02080554, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'roi', 'open_date': datetime.datetime(2025, 8, 7, 11, 0, 24, 532747, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 8, 8, 2, 49, 27, 490000, tzinfo=datetime.timezone.utc), 'stake_amount': 332.416, 'stake_currency': 'USDT', 'base_currency': 'BTC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 6.91955257, 'final_profit_ratio': 0.02080554041543918, 'is_final_exit': True}
2025-08-08 02:49:28,031 - cooldown_period - INFO - Cooldown for BTC/USDT:USDT for 1 candle.
2025-08-08 02:49:28,082 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': protection_trigger, 'base_currency': 'BTC', 'id': 18, 'pair': 'BTC/USDT:USDT', 'lock_time': '2025-08-08 02:49:27', 'lock_timestamp': 1754621367954, 'lock_end_time': '2025-08-08 03:30:00', 'lock_end_timestamp': 1754623800000, 'reason': 'Cooldown period for for 1 candle.', 'side': '*', 'active': True}
2025-08-08 02:49:28,391 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 02:49:52,466 - freqtrade.freqtradebot - INFO - Cancelling current stoploss on exchange for pair SUI/USDT:USDT (orderid:dry_run_sell_SUI/USDT:USDT_1754617504.343189) in order to add another one ...
2025-08-08 02:49:52,523 - freqtrade.freqtradebot - INFO - Cancelling stoploss on exchange for Trade(id=10, pair=SUI/USDT:USDT, amount=810.50000000, is_short=False, leverage=8.0, open_rate=3.74730000, open_since=2025-08-08 01:34:46) order: dry_run_sell_SUI/USDT:USDT_1754617504.343189
2025-08-08 02:50:00,425 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:50:53,118 - freqtrade.freqtradebot - INFO - Cancelling current stoploss on exchange for pair SUI/USDT:USDT (orderid:dry_run_sell_SUI/USDT:USDT_1754621392.531212) in order to add another one ...
2025-08-08 02:50:53,135 - freqtrade.freqtradebot - INFO - Cancelling stoploss on exchange for Trade(id=10, pair=SUI/USDT:USDT, amount=810.50000000, is_short=False, leverage=8.0, open_rate=3.74730000, open_since=2025-08-08 01:34:46) order: dry_run_sell_SUI/USDT:USDT_1754621392.531212
2025-08-08 02:51:01,029 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:52:01,095 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:53:01,185 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:53:33,590 - freqtrade.freqtradebot - INFO - Exit for SUI/USDT:USDT detected. Reason: trailing_stop_loss
2025-08-08 02:53:35,676 - freqtrade.freqtradebot - INFO - Cancelling stoploss on exchange for Trade(id=10, pair=SUI/USDT:USDT, amount=810.50000000, is_short=False, leverage=8.0, open_rate=3.74730000, open_since=2025-08-08 01:34:46) order: dry_run_sell_SUI/USDT:USDT_1754621453.155964
2025-08-08 02:53:35,754 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 02:53:36,017 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 10, 'exchange': 'Binance', 'pair': 'SUI/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'gain': 'profit', 'limit': 3.7756, 'order_rate': 3.7756, 'order_type': 'market', 'amount': 810.5, 'open_rate': 3.7473, 'close_rate': 3.7756, 'current_rate': 3.7756, 'profit_amount': 19.88849477, 'profit_ratio': 0.05236044, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'trailing_stop_loss', 'open_date': datetime.datetime(2025, 8, 8, 1, 34, 46, 917751, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 8, 8, 2, 53, 36, 17050, tzinfo=datetime.timezone.utc), 'stake_amount': 379.64833125, 'stake_currency': 'USDT', 'base_currency': 'SUI', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-08-08 02:53:36,060 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=10, pair=SUI/USDT:USDT, amount=810.50000000, is_short=False, leverage=8.0, open_rate=3.74730000, open_since=2025-08-08 01:34:46)
2025-08-08 02:53:36,233 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=10, pair=SUI/USDT:USDT, amount=810.50000000, is_short=False, leverage=8.0, open_rate=3.74730000, open_since=2025-08-08 01:34:46) [sell]: 1.5301024 USDT - rate: 0.0005
2025-08-08 02:53:36,260 - freqtrade.persistence.trade_model - INFO - Updating trade (id=10) ...
2025-08-08 02:53:36,265 - freqtrade.persistence.trade_model - INFO - MARKET_SELL has been fulfilled for Trade(id=10, pair=SUI/USDT:USDT, amount=810.50000000, is_short=False, leverage=8.0, open_rate=3.74730000, open_since=2025-08-08 01:34:46).
2025-08-08 02:53:36,303 - freqtrade.persistence.trade_model - INFO - Marking Trade(id=10, pair=SUI/USDT:USDT, amount=810.50000000, is_short=False, leverage=8.0, open_rate=3.74730000, open_since=closed) as closed as the trade is fulfilled and found no open orders for it.
2025-08-08 02:53:36,443 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 02:53:36,453 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit_fill, 'trade_id': 10, 'exchange': 'Binance', 'pair': 'SUI/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'gain': 'profit', 'limit': 3.7757, 'order_rate': 3.7757, 'order_type': 'market', 'amount': 810.5, 'open_rate': 3.7473, 'close_rate': 3.7757, 'current_rate': None, 'profit_amount': 19.96950425, 'profit_ratio': 0.05257372, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'trailing_stop_loss', 'open_date': datetime.datetime(2025, 8, 8, 1, 34, 46, 917751, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 8, 8, 2, 53, 36, 6000, tzinfo=datetime.timezone.utc), 'stake_amount': 379.64833125, 'stake_currency': 'USDT', 'base_currency': 'SUI', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 19.96950425, 'final_profit_ratio': 0.05257371847793713, 'is_final_exit': True}
2025-08-08 02:53:36,530 - cooldown_period - INFO - Cooldown for SUI/USDT:USDT for 1 candle.
2025-08-08 02:53:36,696 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': protection_trigger, 'base_currency': 'SUI', 'id': 20, 'pair': 'SUI/USDT:USDT', 'lock_time': '2025-08-08 02:53:36', 'lock_timestamp': 1754621616483, 'lock_end_time': '2025-08-08 03:30:00', 'lock_end_timestamp': 1754623800000, 'reason': 'Cooldown period for for 1 candle.', 'side': '*', 'active': True}
2025-08-08 02:53:36,940 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 02:54:02,975 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:54:16,049 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(486206cf, ('172.18.0.1', 53200))
2025-08-08 02:54:16,068 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(09200972, ('172.18.0.1', 53192))
2025-08-08 02:54:17,883 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(bc7a4ba8, ('172.18.0.1', 33128))
2025-08-08 02:55:03,019 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:56:03,041 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:57:04,146 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:58:04,174 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 02:59:04,204 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:00:09,499 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:00:10,490 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for XRP/USDT:USDT with stake_amount: 393.9226458717 and price: 3.3391 ...
2025-08-08 03:00:10,659 - freqtrade.freqtradebot - INFO - Order dry_run_buy_XRP/USDT:USDT_1754622010.501255 was created for XRP/USDT:USDT and status is closed.
2025-08-08 03:00:11,643 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:00:11,646 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 11, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'XRP/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'limit': 3.3391, 'open_rate': 3.3391, 'order_type': 'market', 'stake_amount': 393.88858375, 'stake_currency': 'USDT', 'base_currency': 'XRP', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 943.7, 'open_date': datetime.datetime(2025, 8, 8, 3, 0, 10, 661323, tzinfo=datetime.timezone.utc), 'current_rate': 3.3391, 'sub_trade': False}
2025-08-08 03:00:11,660 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=2025-08-08 03:00:10)
2025-08-08 03:00:11,703 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=2025-08-08 03:00:10) [buy]: 1.5755543 USDT - rate: 0.0005
2025-08-08 03:00:11,713 - freqtrade.persistence.trade_model - INFO - Updating trade (id=11) ...
2025-08-08 03:00:11,714 - freqtrade.persistence.trade_model - INFO - MARKET_BUY has been fulfilled for Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=2025-08-08 03:00:10).
2025-08-08 03:00:11,790 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:00:11,856 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 11, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'XRP/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'limit': 3.3391, 'open_rate': 3.3391, 'order_type': 'market', 'stake_amount': 393.88858375, 'stake_currency': 'USDT', 'base_currency': 'XRP', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 943.7, 'open_date': datetime.datetime(2025, 8, 8, 3, 0, 10, 661323, tzinfo=datetime.timezone.utc), 'current_rate': 3.3391, 'sub_trade': False}
2025-08-08 03:00:11,858 - freqtrade.rpc.telegram - INFO - Notification 'entry_fill' not sent.
2025-08-08 03:00:11,862 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:01:04,021 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:01:12,314 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:02:12,335 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:03:12,357 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:04:12,394 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:05:12,421 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:06:12,547 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:07:12,566 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:08:12,586 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:09:12,608 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:10:12,628 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:11:12,644 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:11:58,716 - freqtrade.freqtradebot - INFO - Cancelling current stoploss on exchange for pair XRP/USDT:USDT (orderid:dry_run_sell_XRP/USDT:USDT_1754622021.891054) in order to add another one ...
2025-08-08 03:11:58,723 - freqtrade.freqtradebot - INFO - Cancelling stoploss on exchange for Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=2025-08-08 03:00:10) order: dry_run_sell_XRP/USDT:USDT_1754622021.891054
2025-08-08 03:12:12,727 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:12:58,811 - freqtrade.freqtradebot - INFO - Cancelling current stoploss on exchange for pair XRP/USDT:USDT (orderid:dry_run_sell_XRP/USDT:USDT_1754622718.735996) in order to add another one ...
2025-08-08 03:12:58,813 - freqtrade.freqtradebot - INFO - Cancelling stoploss on exchange for Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=2025-08-08 03:00:10) order: dry_run_sell_XRP/USDT:USDT_1754622718.735996
2025-08-08 03:13:12,743 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:14:12,761 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:14:30,988 - freqtrade.freqtradebot - INFO - Exit for XRP/USDT:USDT detected. Reason: trailing_stop_loss
2025-08-08 03:14:33,653 - freqtrade.freqtradebot - INFO - Cancelling stoploss on exchange for Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=2025-08-08 03:00:10) order: dry_run_sell_XRP/USDT:USDT_1754622778.822179
2025-08-08 03:14:33,671 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:14:33,830 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 11, 'exchange': 'Binance', 'pair': 'XRP/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'gain': 'profit', 'limit': 3.3662, 'order_rate': 3.3662, 'order_type': 'market', 'amount': 943.7, 'open_rate': 3.3391, 'close_rate': 3.3662, 'current_rate': 3.3662, 'profit_amount': 22.41037419, 'profit_ratio': 0.05686678, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'trailing_stop_loss', 'open_date': datetime.datetime(2025, 8, 8, 3, 0, 10, 661323, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 8, 8, 3, 14, 33, 829594, tzinfo=datetime.timezone.utc), 'stake_amount': 393.88858375, 'stake_currency': 'USDT', 'base_currency': 'XRP', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-08-08 03:14:33,853 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=2025-08-08 03:00:10)
2025-08-08 03:14:33,947 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=2025-08-08 03:00:10) [sell]: 1.5880584 USDT - rate: 0.0005
2025-08-08 03:14:33,962 - freqtrade.persistence.trade_model - INFO - Updating trade (id=11) ...
2025-08-08 03:14:33,967 - freqtrade.persistence.trade_model - INFO - MARKET_SELL has been fulfilled for Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=2025-08-08 03:00:10).
2025-08-08 03:14:33,971 - freqtrade.persistence.trade_model - INFO - Marking Trade(id=11, pair=XRP/USDT:USDT, amount=943.70000000, is_short=False, leverage=8.0, open_rate=3.33910000, open_since=closed) as closed as the trade is fulfilled and found no open orders for it.
2025-08-08 03:14:33,987 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:14:33,992 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit_fill, 'trade_id': 11, 'exchange': 'Binance', 'pair': 'XRP/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'gain': 'profit', 'limit': 3.3656, 'order_rate': 3.3656, 'order_type': 'market', 'amount': 943.7, 'open_rate': 3.3391, 'close_rate': 3.3656, 'current_rate': None, 'profit_amount': 21.8444373, 'profit_ratio': 0.0554307, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'trailing_stop_loss', 'open_date': datetime.datetime(2025, 8, 8, 3, 0, 10, 661323, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 8, 8, 3, 14, 33, 823000, tzinfo=datetime.timezone.utc), 'stake_amount': 393.88858375, 'stake_currency': 'USDT', 'base_currency': 'XRP', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 21.8444373, 'final_profit_ratio': 0.0554307015752145, 'is_final_exit': True}
2025-08-08 03:14:34,064 - cooldown_period - INFO - Cooldown for XRP/USDT:USDT for 1 candle.
2025-08-08 03:14:34,084 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': protection_trigger, 'base_currency': 'XRP', 'id': 22, 'pair': 'XRP/USDT:USDT', 'lock_time': '2025-08-08 03:14:34', 'lock_timestamp': 1754622874012, 'lock_end_time': '2025-08-08 04:00:00', 'lock_end_timestamp': 1754625600000, 'reason': 'Cooldown period for for 1 candle.', 'side': '*', 'active': True}
2025-08-08 03:14:34,150 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:14:34,180 - freqtrade.freqtradebot - INFO - Pair XRP/USDT:USDT * is locked until 2025-08-08 04:00:00 due to Cooldown period for for 1 candle..
2025-08-08 03:15:14,196 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:16:14,215 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:17:14,234 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:18:14,283 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:19:14,303 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:20:14,323 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:21:14,340 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:22:14,391 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:23:14,410 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:24:14,427 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:25:14,476 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:26:14,499 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:27:14,521 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:28:14,540 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:29:14,560 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:30:06,720 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:30:07,445 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for ETH/USDT:USDT with stake_amount: 401.13131018070004 and price: 3948.82 ...
2025-08-08 03:30:07,642 - freqtrade.freqtradebot - INFO - Order dry_run_buy_ETH/USDT:USDT_1754623807.449008 was created for ETH/USDT:USDT and status is closed.
2025-08-08 03:30:08,453 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:30:08,457 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 12, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'ETH/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'limit': 3948.82, 'open_rate': 3948.82, 'order_type': 'market', 'stake_amount': 400.80523, 'stake_currency': 'USDT', 'base_currency': 'ETH', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 0.812, 'open_date': datetime.datetime(2025, 8, 8, 3, 30, 7, 644274, tzinfo=datetime.timezone.utc), 'current_rate': 3948.82, 'sub_trade': False}
2025-08-08 03:30:08,463 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=12, pair=ETH/USDT:USDT, amount=0.81200000, is_short=False, leverage=8.0, open_rate=3948.82000000, open_since=2025-08-08 03:30:07)
2025-08-08 03:30:08,476 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=12, pair=ETH/USDT:USDT, amount=0.81200000, is_short=False, leverage=8.0, open_rate=3948.82000000, open_since=2025-08-08 03:30:07) [buy]: 1.6032209 USDT - rate: 0.0005
2025-08-08 03:30:08,478 - freqtrade.persistence.trade_model - INFO - Updating trade (id=12) ...
2025-08-08 03:30:08,480 - freqtrade.persistence.trade_model - INFO - MARKET_BUY has been fulfilled for Trade(id=12, pair=ETH/USDT:USDT, amount=0.81200000, is_short=False, leverage=8.0, open_rate=3948.82000000, open_since=2025-08-08 03:30:07).
2025-08-08 03:30:08,500 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:30:08,507 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 12, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'ETH/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'limit': 3948.82, 'open_rate': 3948.82, 'order_type': 'market', 'stake_amount': 400.80523, 'stake_currency': 'USDT', 'base_currency': 'ETH', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 0.812, 'open_date': datetime.datetime(2025, 8, 8, 3, 30, 7, 644274, tzinfo=datetime.timezone.utc), 'current_rate': 3948.82, 'sub_trade': False}
2025-08-08 03:30:08,511 - freqtrade.rpc.telegram - INFO - Notification 'entry_fill' not sent.
2025-08-08 03:30:08,519 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:30:08,677 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for SOL/USDT:USDT with stake_amount: 401.13131018070004 and price: 175.56 ...
2025-08-08 03:30:08,817 - freqtrade.freqtradebot - INFO - Order dry_run_buy_SOL/USDT:USDT_1754623808.678986 was created for SOL/USDT:USDT and status is closed.
2025-08-08 03:30:09,604 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:30:09,605 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 13, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'SOL/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'limit': 175.56, 'open_rate': 175.56, 'order_type': 'market', 'stake_amount': 400.93515, 'stake_currency': 'USDT', 'base_currency': 'SOL', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 18.27, 'open_date': datetime.datetime(2025, 8, 8, 3, 30, 8, 818890, tzinfo=datetime.timezone.utc), 'current_rate': 175.56, 'sub_trade': False}
2025-08-08 03:30:09,605 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=13, pair=SOL/USDT:USDT, amount=18.27000000, is_short=False, leverage=8.0, open_rate=175.56000000, open_since=2025-08-08 03:30:08)
2025-08-08 03:30:09,612 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=13, pair=SOL/USDT:USDT, amount=18.27000000, is_short=False, leverage=8.0, open_rate=175.56000000, open_since=2025-08-08 03:30:08) [buy]: 1.6037406 USDT - rate: 0.0005
2025-08-08 03:30:09,613 - freqtrade.persistence.trade_model - INFO - Updating trade (id=13) ...
2025-08-08 03:30:09,613 - freqtrade.persistence.trade_model - INFO - MARKET_BUY has been fulfilled for Trade(id=13, pair=SOL/USDT:USDT, amount=18.27000000, is_short=False, leverage=8.0, open_rate=175.56000000, open_since=2025-08-08 03:30:08).
2025-08-08 03:30:09,619 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:30:09,622 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 13, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'SOL/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'limit': 175.56, 'open_rate': 175.56, 'order_type': 'market', 'stake_amount': 400.93515, 'stake_currency': 'USDT', 'base_currency': 'SOL', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 18.27, 'open_date': datetime.datetime(2025, 8, 8, 3, 30, 8, 818890, tzinfo=datetime.timezone.utc), 'current_rate': 175.56, 'sub_trade': False}
2025-08-08 03:30:09,624 - freqtrade.rpc.telegram - INFO - Notification 'entry_fill' not sent.
2025-08-08 03:30:26,136 - freqtrade.exchange.common - WARNING - fetch_l2_order_book() returned exception: "Could not get order book due to RequestTimeout. Message: binance GET https://fapi.binance.com/fapi/v1/depth?symbol=ETHUSDT&limit=20". Retrying still for 4 times.
2025-08-08 03:30:29,049 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:31:05,549 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 03:31:29,560 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:32:29,679 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:33:29,700 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:34:29,718 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:35:29,735 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:36:29,752 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:37:29,799 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:38:29,822 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:39:29,842 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:40:29,859 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:41:29,878 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:42:29,917 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:43:29,936 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:44:29,954 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:44:34,514 - freqtrade.freqtradebot - INFO - Pair XRP/USDT:USDT * is locked until 2025-08-08 04:00:00 due to Cooldown period for for 1 candle..
2025-08-08 03:45:12,700 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 6 pairs: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'SOL/USDT:USDT', 'SUI/USDT:USDT', 'DOGE/USDT:USDT', 'XRP/USDT:USDT']
2025-08-08 03:45:31,225 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:46:31,243 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:47:31,264 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:48:31,283 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:49:31,301 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:50:31,351 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:51:31,379 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:52:31,400 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:53:31,446 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:54:31,467 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:55:31,487 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:56:31,519 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:57:31,537 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:58:31,556 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 03:59:31,576 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:00:34,151 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 04:00:34,449 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for XRP/USDT:USDT with stake_amount: 401.13131018070004 and price: 3.3766 ...
2025-08-08 04:00:34,707 - freqtrade.freqtradebot - INFO - Order dry_run_buy_XRP/USDT:USDT_1754625634.460142 was created for XRP/USDT:USDT and status is closed.
2025-08-08 04:00:36,306 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 04:00:36,309 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 14, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'XRP/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'limit': 3.3766, 'open_rate': 3.3766, 'order_type': 'market', 'stake_amount': 401.0978725, 'stake_currency': 'USDT', 'base_currency': 'XRP', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 950.3, 'open_date': datetime.datetime(2025, 8, 8, 4, 0, 34, 708690, tzinfo=datetime.timezone.utc), 'current_rate': 3.3766, 'sub_trade': False}
2025-08-08 04:00:36,327 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=14, pair=XRP/USDT:USDT, amount=950.30000000, is_short=False, leverage=8.0, open_rate=3.37660000, open_since=2025-08-08 04:00:34)
2025-08-08 04:00:36,370 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=14, pair=XRP/USDT:USDT, amount=950.30000000, is_short=False, leverage=8.0, open_rate=3.37660000, open_since=2025-08-08 04:00:34) [buy]: 1.6043915 USDT - rate: 0.0005
2025-08-08 04:00:36,372 - freqtrade.persistence.trade_model - INFO - Updating trade (id=14) ...
2025-08-08 04:00:36,374 - freqtrade.persistence.trade_model - INFO - MARKET_BUY has been fulfilled for Trade(id=14, pair=XRP/USDT:USDT, amount=950.30000000, is_short=False, leverage=8.0, open_rate=3.37660000, open_since=2025-08-08 04:00:34).
2025-08-08 04:00:36,448 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 04:00:36,455 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 14, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'XRP/USDT:USDT', 'leverage': 8.0, 'direction': 'Long', 'limit': 3.3766, 'open_rate': 3.3766, 'order_type': 'market', 'stake_amount': 401.0978725, 'stake_currency': 'USDT', 'base_currency': 'XRP', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 950.3, 'open_date': datetime.datetime(2025, 8, 8, 4, 0, 34, 708690, tzinfo=datetime.timezone.utc), 'current_rate': 3.3766, 'sub_trade': False}
2025-08-08 04:00:36,457 - freqtrade.rpc.telegram - INFO - Notification 'entry_fill' not sent.
2025-08-08 04:00:36,472 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:01:05,993 - freqtrade.wallets - INFO - Wallets synced.
2025-08-08 04:01:38,015 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:02:38,041 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:03:38,060 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:04:38,079 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:05:38,108 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:06:38,128 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:07:38,185 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:08:38,205 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:09:38,228 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:10:38,635 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:11:38,653 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:12:38,681 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:13:38,700 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:14:38,719 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:15:38,738 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:16:38,757 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:17:38,801 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:18:38,819 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:19:38,838 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:20:38,859 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:21:38,879 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:22:38,898 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:23:38,923 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
2025-08-08 04:24:38,958 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.6', state='RUNNING'
